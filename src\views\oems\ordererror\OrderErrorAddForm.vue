<template>
  <Dialog title="上报异常" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-divider content-position="left">运单信息</el-divider>

      <el-row>
        <el-col :span="12">
          <el-form-item label="运单号" prop="waybillNo">
            <el-input v-model="formData.waybillNo" placeholder="请输入" :disabled="waybillNoReadonly" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票时间" prop="createTime">
            <el-date-picker v-model="formDatacs.createTime" type="date" value-format="x" disabled />
          </el-form-item>
          <!-- <el-form-item label="运单" prop="waybillId">
            <el-input v-model="formData.waybillId" disabled/>
          </el-form-item> -->
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="货品名称">
            <el-input v-model="formDatacs.goodsList[0].goodsName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="包装">
            <el-select v-model="formDatacs.goodsList[0].goodsPackageUnit" disabled>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.GOODS_PACKAGE_UNIT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数量">
            <el-input v-model="formDatacs.goodsList[0].goodsNum" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="车次ID" prop="voyageId">
            <el-input v-model="formData.voyageId" disabled />
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <VehicleSelector
            propName="vehicleId"
            v-model="formData.vehicleId"
            :label="$t('车牌号')"
            :placeholder="$t('请输入车牌号')"
            @vehicle-selected="handleVehicleSelected"
          />
          <!-- <el-form-item label="车牌号" prop="vehicleLicenseNumber">
            <el-input v-model="formData.vehicleLicenseNumber" placeholder="请输入车牌号" />
          </el-form-item> -->
        </el-col>
      </el-row>

      <!-- <el-form-item label="异常状态" prop="errorStatus">
        <el-select v-model="formData.errorStatus" placeholder="请选择异常状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_ERROR_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

      <el-divider content-position="left">异常上报</el-divider>

      <el-row>
        <el-col :span="12">
          <el-form-item label="上报站点" prop="reportBranchName">
            <el-input v-model="formData.reportBranchName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报时间" prop="reportTime">
            <el-date-picker
              v-model="formData.reportTime"
              type="date"
              value-format="x"
              placeholder="选择上报时间"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- <el-form-item label="上报类型" prop="reportType">
        <el-select v-model="formData.reportType" placeholder="请选择上报类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_CREATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

      <el-row>
        <el-col :span="12">
          <el-form-item label="异常类型" prop="errorType">
            <el-select v-model="formData.errorType" placeholder="请选择异常类型">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_ERROR_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="异常件数" prop="errorNum">
            <el-input v-model="formData.errorNum" placeholder="请输入异常件数" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="是否需要回复" prop="needReply">
            <el-radio-group v-model="formData.needReply">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.TINYINT_BOOLEAN)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <BranchSelector
            propName="replyBranchId"
            v-model="formData.replyBranchId"
            :label="$t('回复站点')"
            :placeholder="$t('请输入站点名称')"
            @branch-selected="handleBranchSelected2"
          />
        </el-col>
      </el-row>

      <el-form-item label="是否拦截运单" prop="interceptStatus">
        <el-radio-group v-model="formData.interceptStatus">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.TINYINT_BOOLEAN)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="异常备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入原因" />
      </el-form-item>
      <el-form-item label="异常图片" prop="errorImgUrl">
        <UploadImg v-model="formData.errorImgUrl" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderErrorApi, OrderErrorVO } from '@/api/oems/ordererror'

import { WaybillApi, WaybillVO } from '@/api/wbms/waybill'

/** 运输异常 表单 */
defineOptions({ name: 'OrderErrorForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const waybillNoReadonly = ref(false) // 运单号是否只读：1）修改时的数据加载；2）提交的按钮禁用
const props = defineProps({
  loginDept: {
    type: Object,
    default: null
  }
})
const formData = ref({
  id: undefined,
  waybillId: undefined,
  waybillNo: undefined,
  errorStatus: undefined,
  voyageId: undefined,
  vehicleId: undefined,
  vehicleLicenseNumber: undefined,
  reportBranchId: undefined,
  reportTime: undefined,
  reportBranchName: undefined,
  reportType: undefined,
  replyBranchId: undefined,
  replyTime: undefined,
  replyBranchName: undefined,
  needReply: undefined,
  errorNum: undefined,
  errorType: undefined,
  cancelReason: undefined,
  finishTime: undefined,
  finishName: undefined,
  interceptStatus: undefined,
  errorImgUrl: undefined,
  remark: undefined
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref

const formDatacs = ref({
  goodsList: [
    {
      goodsNo: 0,
      goodsName: undefined,
      goodsPackageUnit: undefined,
      goodsNum: undefined,
      goodsVolume: undefined,
      goodsWeight: undefined,
      goodsPrice: undefined
    }
  ]
})

/** 打开弹窗 */
const open = async (waybillNo?: String, readonly?: boolean) => {
  dialogVisible.value = true
  resetForm()
  formData.value.reportBranchId = props.loginDept.id
  formData.value.reportBranchName = props.loginDept.name

  if (waybillNo) {
    formData.value.waybillNo = waybillNo
    waybillNoReadonly.value = readonly || false
  } else {
    waybillNoReadonly.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderErrorVO
    console.log(formData.value)
    await OrderErrorApi.createOrderError(data)
    message.success(t('common.createSuccess'))

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    waybillId: undefined,
    waybillNo: undefined,
    errorStatus: undefined,
    voyageId: undefined,
    vehicleLicenseNumber: undefined,
    reportBranchId: undefined,
    reportTime: undefined,
    reportBranchName: undefined,
    reportType: undefined,
    replyBranchId: undefined,
    replyTime: undefined,
    replyBranchName: undefined,
    needReply: undefined,
    errorNum: undefined,
    errorType: undefined,
    cancelReason: undefined,
    finishTime: undefined,
    finishName: undefined,
    interceptStatus: undefined,
    errorImgUrl: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}

const handleBranchSelected1 = (branchInfo) => {
  formData.value.reportBranchName = branchInfo.branchName
}

const handleBranchSelected2 = (branchInfo) => {
  formData.value.replyBranchName = branchInfo.branchName
}

// const changeWaybill = async() => {
//   const data = WaybillApi.getWaybill(formData.value.waybillId)
//   formDatacs.value = data
//   console.log(formDatacs.value);
//   console.log(formDatacs.value.waybillNo);
//   formData.value.waybillNo = formDatacs.value.waybillNo
// }

watch(
  () => formData.value.waybillNo,
  async (newVal) => {
    if (newVal !== undefined) {
      try {
        // 确保 await 获取到数据
        const data = await WaybillApi.getWaybillByWaybillNo(newVal)
        formDatacs.value = data
        console.log(formDatacs.value)
      } catch (error) {
        console.error('获取运单信息失败:', error)
      }
    }
  }
)

const handleVehicleSelected = (vehicleInfo) => {
  formData.value.vehicleLicenseNumber = vehicleInfo.vehicleLicenseNumber
  // formData.value.vehicleType = vehicleInfo.vehicleType
}

// const changeWaybill = async () => {
//   try {
//     // 确保 await 获取到数据
//     const data = await WaybillApi.getWaybillByWaybillNo(formData.value.waybillNo)
//     formDatacs.value = data
//     console.log(formDatacs.value)
//     console.log(formDatacs.value.waybillNo)
//     formData.value.waybillNo = formDatacs.value.waybillNo
//   } catch (error) {
//     console.error('获取运单信息失败:', error)
//   }
// }
</script>