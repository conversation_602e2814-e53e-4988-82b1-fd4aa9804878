<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="运单编号" prop="waybillNo">
        <el-input
          v-model="queryParams.waybillNo"
          placeholder="请输入运单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="异常状态" prop="errrorStatus">
        <el-select
          v-model="queryParams.errorStatus"
          placeholder="请选择异常状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_ERROR_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车牌号" prop="vehicleLicenseNumber">
        <el-input
          v-model="queryParams.vehicleLicenseNumber"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <BranchSelector
        propName="reportBranchId"
        v-model="queryParams.reportBranchId"
        :label="$t('上报站点')"
        :placeholder="$t('请输入站点名称')"
      />
      <el-form-item label="上报时间" prop="reportTime">
        <el-date-picker
          v-model="queryParams.reportTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="上报类型" prop="reportType">
        <el-select
          v-model="queryParams.reportType"
          placeholder="请选择上报类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_CREATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="异常类型" prop="errorType">
        <el-select
          v-model="queryParams.errorType"
          placeholder="请选择异常类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_ERROR_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['oems:order-error:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openAddForm"
          v-hasPermi="['oems:order-error:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 上报异常
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openRecordForm"
          v-hasPermi="['oems:order-error:update']"
        >
          <Icon icon="ep:edit" class="mr-5px" />异常回复
        </el-button>
        <el-button
          type="primary"
          plain
          @click="updateErrorStatus(3)"
          v-hasPermi="['oems:order-error:update']"
        >
          <Icon icon="ep:edit" class="mr-5px" />异常结束
        </el-button>
        <el-button
          type="primary"
          plain
          @click="updateErrorStatus(4)"
          v-hasPermi="['oems:order-error:update']"
        >
          <Icon icon="ep:edit" class="mr-5px" />取消上报
        </el-button>
        <!-- <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['oems:order-error:delete']"
          >
            删除
          </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" fixed />
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column fixed label="运单编号" align="center" prop="waybillNo" min-width="160px">
        <template #default="scope">
          <el-link @click="handleWaybillNoClick(scope.row.waybillNo)">
            {{ scope.row.waybillNo }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="异常状态" align="center" prop="errrorStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.OEMS_ERROR_STATUS" :value="scope.row.errorStatus" />
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" prop="vehicleLicenseNumber" />
      <el-table-column
        label="上报时间"
        align="center"
        prop="reportTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="上报站点名" align="center" prop="reportBranchName" />
      <el-table-column label="上报类型" align="center" prop="reportType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.OEMS_CREATE_TYPE" :value="scope.row.reportType" />
        </template>
      </el-table-column>
      <el-table-column
        label="回复时间"
        align="center"
        prop="replyTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="回复站点名" align="center" prop="replyBranchName" />
      <el-table-column label="是否需要回复" align="center" prop="needReply">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TINYINT_BOOLEAN" :value="scope.row.needReply" />
        </template>
      </el-table-column>
      <el-table-column label="异常件数" align="center" prop="errorNum" />
      <el-table-column label="异常类型" align="center" prop="errorType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.OEMS_ERROR_TYPE" :value="scope.row.errorType" />
        </template>
      </el-table-column>
      <el-table-column label="取消原因" align="center" prop="cancelReason" />
      <el-table-column
        label="异常结束时间"
        align="center"
        prop="finishTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="异常结束操作人员" align="center" prop="finishName" />
      <el-table-column label="备注" align="center" prop="remark" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加 -->
  <OrderErrorAddForm ref="formRefAdd" @success="getList" :loginDept="loginDept" />

  <!-- 表单弹窗：修改 -->
  <OrderErrorRecordsForm ref="formRefRecord" @success="getList" :loginDept="loginDept" />

  <!-- 取消原因弹窗 -->
  <Dialog title="取消上报" v-model="cancelDialogVisible" width="500px">
    <el-form
      ref="cancelFormRef"
      :model="cancelForm"
      :rules="cancelFormRules"
      label-width="100px"
      v-loading="cancelFormLoading"
    >
      <el-form-item label="取消原因" prop="cancelReason">
        <el-input
          v-model="cancelForm.cancelReason"
          type="textarea"
          :rows="4"
          placeholder="请输入取消原因"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCancel" :loading="cancelFormLoading">确定</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderErrorApi, OrderErrorVO } from '@/api/oems/ordererror'
import OrderErrorAddForm from './OrderErrorAddForm.vue'
import OrderErrorRecordsForm from './OrderErrorRecordsForm.vue'
import * as DeptApi from '@/api/system/dept'
import {useRouter,useRoute} from 'vue-router'

/** 运输异常 列表 */
defineOptions({ name: 'OrderError' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OrderErrorVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  waybillNo: undefined,
  errorStatus: undefined,
  vehicleLicenseNumber: undefined,
  reportBranchId: undefined,
  reportTime: [],
  reportType: undefined,
  errorType: undefined
})
const router = useRouter()
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 用于保存当前选中的行
const selectedRows = ref<OrderErrorVO[]>([])

const loginDept = ref()

// 取消原因弹窗相关
const cancelDialogVisible = ref(false)
const cancelFormRef = ref()
const cancelFormLoading = ref(false)
const cancelForm = reactive({
  cancelReason: ''
})
const cancelFormRules = reactive({
  cancelReason: [{ required: true, message: '请输入取消原因', trigger: 'blur' }]
})



/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderErrorApi.getOrderErrorPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleWaybillNoClick = (waybillNo:string) => {
    router.push({ path: '/wbms/waybillDetailNew', query: { waybillNo: waybillNo } })
    // 你可以在这里添加更多的逻辑
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加操作 */
const formRefAdd = ref()
const openAddForm = () => {
  formRefAdd.value.open()
}

/** 修改操作 */
const formRefRecord = ref()
const openRecordForm = () => {
  if (selectedRows.value.length === 0) {
    message.warning(t('error.selectionError'))
    return
  } else if (selectedRows.value.length > 1) {
    message.warning(t('error.selectionOneError'))
    return
  }
  if (selectedRows.value[0].errorStatus === 3 || selectedRows.value[0].errorStatus === 4) {
    message.warning(t('请选择未回复或已回复的运单信息!'))
    return
  }
  formRefRecord.value.open(selectedRows.value[0].id)
}

/** 处理表格选择变化 */
const handleSelectionChange = (rows: OrderErrorVO[]) => {
  selectedRows.value = rows
}

/** 异常 */
const updateErrorStatus = async (status: number) => {
  if (selectedRows.value.length === 0) {
    message.warning(t('error.selectionError'))
    return
  } else if (selectedRows.value.length > 1) {
    message.warning(t('error.selectionOneError'))
    return
  }
  if (status == 3) {
    // 检查异常状态，只有已回复（状态为2）的可以结束
    if (selectedRows.value[0].errorStatus !== 2) {
      message.error(t('只有已回复的异常才能结束!'))
      return
    }
    await message.confirm(t('是否确认结束?'))
  } else if (status == 4) {
    // 取消上报：检查状态，只有未回复（1）和已回复（2）的可以取消上报
    if (selectedRows.value[0].errorStatus !== 1 && selectedRows.value[0].errorStatus !== 2) {
      message.error(t('只有未回复或已回复的异常才能取消上报!'))
      return
    }
    // 打开取消原因弹窗
    cancelForm.cancelReason = ''
    cancelDialogVisible.value = true
    return
  }
  selectedRows.value[0].errorStatus = status
  await OrderErrorApi.updateOrderError(selectedRows.value[0])
  message.success(t('common.updateSuccess'))
  // 刷新列表
  getList()
}

/** 确认取消上报 */
const confirmCancel = async () => {
  // 校验表单
  if (!cancelFormRef.value) return
  const valid = await cancelFormRef.value.validate()
  if (!valid) return

  try {
    cancelFormLoading.value = true
    // 更新异常状态为取消上报，并保存取消原因
    selectedRows.value[0].errorStatus = 4
    selectedRows.value[0].cancelReason = cancelForm.cancelReason
    await OrderErrorApi.updateOrderError(selectedRows.value[0])
    message.success(t('取消上报成功'))
    // 关闭弹窗
    cancelDialogVisible.value = false
    // 刷新列表
    getList()
  } catch (error) {
    console.error('取消上报失败:', error)
  } finally {
    cancelFormLoading.value = false
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderErrorApi.exportOrderError(queryParams)
    download.excel(data, '运输异常.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  loginDept.value = await DeptApi.getLoginUserDept()

  getList()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
