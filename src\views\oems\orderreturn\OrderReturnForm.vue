<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="运单号" prop="waybillNo">
        <el-input v-model="formData.waybillNo" placeholder="请输入运单号" :disabled="formType === 'applyAudit'" />
      </el-form-item>
      <el-form-item label="退货到达站点" prop="returnBranchName">
        <el-input v-model="formData.returnBranchName" disabled />
      </el-form-item>
      <template v-if="formType === 'apply'">
        <el-form-item label="申请站点" prop="applyBranchId">
          <el-input v-model="formData.applyBranchName" placeholder="请输入申请站点" disabled/>
        </el-form-item>
      </template>
      <el-form-item label="退货原因" prop="applyReason">
        <el-input v-model="formData.applyReason" placeholder="请输入退货原因" :disabled="formType === 'applyAudit'" />
        <!-- <Editor v-model="formData.applyReason" height="150px" /> -->
      </el-form-item>

      <!-- 反退货申请 -->
      <template v-if="formType === 'returnApply'">
        <el-form-item label="反退货申请" prop="returnApplyReason">
          <el-input v-model="formData.returnApplyReason" placeholder="请输入反退货申请" />
          <!-- <Editor v-model="formData.returnApplyReason" height="150px" /> -->
        </el-form-item>
      </template>

      <!-- 反退货审核 -->
      <template v-if="formType === 'returnAudit'">
        <el-form-item label="反退货申请" prop="returnApplyReason">
          <el-input v-model="formData.returnApplyReason" placeholder="请输入反退货申请" />
          <!-- <Editor v-model="formData.returnApplyReason" height="150px" /> -->
        </el-form-item>
        <el-form-item label="反退货驳回信息" prop="returnRejectReason">
          <el-input v-model="formData.returnRejectReason" placeholder="请输入反退货驳回" />
          <!-- <Editor v-model="formData.returnRejectReason" height="150px" /> -->
        </el-form-item>
        <el-form-item label="审批站点" prop="auditBranchId">
          <el-input v-model="formData.auditBranchId" placeholder="请输入审批站点" />
        </el-form-item>
        <!-- <el-form-item label="审批站点" prop="auditBranchName">
        <el-input v-model="formData.auditBranchName" placeholder="请输入审批站点" />
      </el-form-item> -->
      </template>

      <!-- 退货审批 -->
      <template v-if="formType === 'applyAudit'">
        <el-form-item label="审批站点" prop="auditBranchName">
          <el-input v-model="formData.auditBranchName" placeholder="请输入审批站点" disabled/>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button
          @click="rejectForm"
          type="danger"
          :disabled="formLoading"
          v-if="formType === 'applyAudit' || formType === 'returnAudit'"
          >驳 回</el-button
      >
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 驳回原因弹窗 -->
  <Dialog title="驳回原因" v-model="rejectDialogVisible" width="500px">
    <el-form
      ref="rejectFormRef"
      :model="rejectFormData"
      :rules="rejectFormRules"
      label-width="100px"
      v-loading="rejectFormLoading"
    >
      <el-form-item label="驳回原因" prop="rejectReason">
        <el-input
          v-model="rejectFormData.rejectReason"
          type="textarea"
          :rows="4"
          placeholder="请输入驳回原因"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReject" :loading="rejectFormLoading">确定</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderReturnApi, OrderReturnVO } from '@/api/oems/orderreturn'
import { getUserProfile } from '@/api/system/user/profile'
import { EventRouteApi } from '@/api/wbms/eventroute'
/** 退货 表单 */
defineOptions({ name: 'OrderReturnForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  returnNo: undefined,
  returnStatus: undefined,
  waybillId: undefined,
  waybillNo: undefined,
  applyBranchId: undefined,
  applyTime: undefined,
  applyBranchName: undefined,
  applyReason: undefined,
  returnBranchId: undefined,
  returnBranchName: undefined,
  auditBranchId: undefined,
  auditBranchName: undefined,
  auditTime: undefined,
  returnApplyReason: undefined,
  returnRejectReason: undefined,
  rejectReason: undefined,
  remark: undefined
})
const loginDept = reactive({
  deptId: undefined,
  deptName: undefined
})

const loginUserProfile = ref()

const formRules = reactive({})
const formRef = ref() // 表单 Ref

// 驳回弹窗相关
const rejectDialogVisible = ref(false)
const rejectFormRef = ref()
const rejectFormLoading = ref(false)
const rejectFormData = reactive({
  rejectReason: ''
})
const rejectFormRules = reactive({
  rejectReason: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
    resetForm()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderReturnApi.getOrderReturn(id)
    } catch {

    }
  }
    switch (type) {
    case 'apply':
      dialogTitle.value = t('退货申请')
      formData.value.applyBranchId = loginDept.deptId
      formData.value.applyBranchName = loginDept.deptName
      break
    case 'returnApply':
      dialogTitle.value = t('反退货申请')
      break
    case 'returnAudit':
      dialogTitle.value = t('反退货审核')
      break
    case 'applyAudit':
      dialogTitle.value = t('退货审批')
      formData.value.auditBranchId = loginDept.deptId
      formData.value.auditBranchName = loginDept.deptName
      console.log(formData.value.auditBranchName)
      break
  }
  formLoading.value = false

  formType.value = type
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderReturnVO
    if (formType.value === 'apply') {
      // 退货申请
      data.returnStatus = 1 // 1 代表“已申请”
      data.applyBranchId = loginDept.deptId
      data.applyBranchName = loginDept.deptName
      await OrderReturnApi.createOrderReturn(data)
      message.success(t('common.createSuccess'))
    } else if (formType.value === 'returnApply') {
      // 反退货申请
      data.returnStatus = 6 // 6 代表“反退货申请”
      await OrderReturnApi.updateOrderReturn(data)
      message.success(t('common.updateSuccess'))
    } else if (formType.value === 'returnAudit') {
      // 反退货审核
      data.returnStatus = 5 // 5 代表“反退货”
      await OrderReturnApi.updateOrderReturn(data)
      message.success(t('common.updateSuccess'))
    } else if (formType.value === 'applyAudit') {
      // 退货审批
      data.auditBranchId = loginDept.deptId
      data.auditBranchName = loginDept.deptName
      data.returnStatus = 4 // 4 代表“已退货”
      await OrderReturnApi.updateOrderReturn(data)
      message.success(t('common.updateSuccess'))     
    }
    handleSave(formData.value.waybillNo, formType.value, true)
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 保存按钮的点击事件处理函数
const handleSave = async (waybillNo: string, formType: string, success: boolean) => {
  const eventroute = {
    deptId: loginUserProfile.value.dept.id,
    deptName: loginUserProfile.value.dept.name,
    eventNo: waybillNo,
    // 申请退货【蚌埠】因为【测】申请退货
    // 单号【241101001】退货通过
    eventLog: `申请退货[${loginUserProfile.value.dept.name ?? ''}]因为[${formData.value?.applyReason ?? ''}]申请退货`,
    eventNode: '16119',
    eventNodeValue: '退货申请',
    eventType: '16201',
    userCode: loginUserProfile.value.id,
    userName: loginUserProfile.value.username
  }
  if(formType === 'applyAudit' && success){
    eventroute.eventLog = `单号[${waybillNo}]退货通过`
    eventroute.eventNode = '16121'
    eventroute.eventNodeValue = '退货通过'
  }else if(formType === 'applyAudit' && !success){
    eventroute.eventLog = `单号[${waybillNo}]由[${loginUserProfile.value.dept.name ?? ''}]进行驳回，原因：[${formData.value?.rejectReason ?? ''}]`
    eventroute.eventNode = '16124'
    eventroute.eventNodeValue = '退货驳回'
  }else if(formType === 'returnAudit'){
    eventroute.eventLog = `单号[${waybillNo}]由[${loginUserProfile.value.dept.name ?? ''}]进行反退货 `
    eventroute.eventNode = '16123'
    eventroute.eventNodeValue = '反退货'
  }
  await EventRouteApi.createEventRoute(eventroute)
}

const rejectForm = () => {
  // 打开驳回原因弹窗
  rejectFormData.rejectReason = ''
  rejectDialogVisible.value = true



    if (formType.value === 'returnAudit') {
      // 反退货审核
      data.returnStatus = 7 // 7 代表“反退货驳回”
      await OrderReturnApi.updateOrderReturn(data)
      message.success(t('common.updateSuccess'))
    } else if (formType.value === 'applyAudit') {
      // 退货审批
      data.returnStatus = 3 // 3 代表“已驳回”
      await OrderReturnApi.updateOrderReturn(data)
      message.success(t('common.updateSuccess'))
    }
    handleSave(formData.value.waybillNo, formType.value, false)
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 确认驳回 */
const confirmReject = async () => {
  // 校验驳回原因表单
  if (!rejectFormRef.value) return
  const valid = await rejectFormRef.value.validate()
  if (!valid) return

  try {
    rejectFormLoading.value = true
    const data = formData.value as unknown as OrderReturnVO

    if (formType.value === 'returnAudit') {
      // 反退货审核
      data.returnStatus = 7 // 7 代表"反退货驳回"
      data.returnRejectReason = rejectFormData.rejectReason
      await OrderReturnApi.updateOrderReturn(data)
      message.success(t('common.updateSuccess'))
    } else if (formType.value === 'applyAudit') {
      // 退货审批
      data.returnStatus = 3 // 3 代表"已驳回"
      data.rejectReason = rejectFormData.rejectReason
      await OrderReturnApi.updateOrderReturn(data)
      message.success(t('common.updateSuccess'))
    }

    handleSave(formData.value.waybillNo, formType.value, false)
    // 关闭弹窗
    rejectDialogVisible.value = false
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error('驳回操作失败:', error)
  } finally {
    rejectFormLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    returnNo: undefined,
    returnStatus: undefined,
    waybillId: undefined,
    waybillNo: undefined,
    applyBranchId: undefined,
    applyTime: undefined,
    applyBranchName: undefined,
    applyReason: undefined,
    returnBranchId: undefined,
    returnBranchName: undefined,
    auditBranchId: undefined,
    auditBranchName: undefined,
    auditTime: undefined,
    returnApplyReason: undefined,
    returnRejectReason: undefined,
    rejectReason: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}

/** 初始化 **/
onMounted(async () => {
  loginUserProfile.value = await getUserProfile()
    loginDept.deptId = loginUserProfile.value.dept.id,
    loginDept.deptName = loginUserProfile.value.dept.name
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
