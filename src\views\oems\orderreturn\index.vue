<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="运单号" prop="waybillNo">
        <el-input
          v-model="queryParams.waybillNo"
          placeholder="请输入运单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <BranchSelector
        propName="applyBranchId"
        v-model="queryParams.applyBranchId"
        :label="$t('申请站点')"
        :placeholder="$t('请输入站点名称')"
      />
      <!-- <el-form-item label="申请站点ID" prop="applyBranchId">
        <el-input
          v-model="queryParams.applyBranchId"
          placeholder="请输入申请站点ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <BranchSelector
        propName="returnBranchId"
        v-model="queryParams.returnBranchId"
        :label="$t('退货站点')"
        :placeholder="$t('请输入站点名称')"
      />
      <!-- <el-form-item label="退货站点ID" prop="returnBranchId">
        <el-input
          v-model="queryParams.returnBranchId"
          placeholder="请输入退货站点ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="退货状态" prop="returnStatus">
        <el-select
          v-model="queryParams.returnStatus"
          placeholder="请选择退货状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_RETURN_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="queryParams.applyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>

      <el-form-item label="审批时间" prop="auditTime">
        <el-date-picker
          v-model="queryParams.auditTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['oems:order-return:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('apply')"
          v-hasPermi="['oems:order-return:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 退货申请
        </el-button>

        <el-button
          type="primary"
          plain
          @click="openForm('returnApply')"
          v-hasPermi="['oems:order-return:update']"
        >
          <Icon icon="ep:plus" class="mr-5px" />反退货申请
        </el-button>
        <el-button
          type="warning"
          plain
          @click="openForm('returnAudit')"
          v-hasPermi="['oems:order-return:update']"
        >
          <Icon icon="ep:edit" class="mr-5px" />反退货审核
        </el-button>
        <el-button
          type="warning"
          plain
          @click="openForm('applyAudit')"
          v-hasPermi="['oems:order-return:update']"
        >
          <Icon icon="ep:edit" class="mr-5px" />退货审批
        </el-button>
        <el-button
          type="danger"
          plain
          @click="updateReturnStatus()"
          v-hasPermi="['oems:order-return:update']"
        >
          <Icon icon="ep:edit" class="mr-5px" />取消申请
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" fixed />
      <el-table-column label="退货编号" align="center" prop="returnNo" min-width="140px"/>
      <el-table-column label="退货状态" align="center" prop="returnStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.OEMS_RETURN_STATUS" :value="scope.row.returnStatus" />
        </template>
      </el-table-column>
      <el-table-column label="申请部门" align="center" prop="applyBranchName" />
      <el-table-column label="运单号" align="center" prop="waybillNo" />
      <!-- 开票日期 -->
      <el-table-column
        label="申请时间"
        align="center"
        prop="applyTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="申请原因" align="center" prop="applyReason" />
      <el-table-column label="退货部门" align="center" prop="returnBranchName" />
      <el-table-column label="审批部门" align="center" prop="auditBranchName" />
      <el-table-column
        label="审批退货时间"
        align="center"
        prop="auditTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="驳回原因" align="center" prop="rejectReason" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建者" align="center" prop="creator" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderReturnForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderReturnApi, OrderReturnVO } from '@/api/oems/orderreturn'
import OrderReturnForm from './OrderReturnForm.vue'
import { getUserProfile } from '@/api/system/user/profile'
import { EventRouteApi } from '@/api/wbms/eventroute'

/** 退货 列表 */
defineOptions({ name: 'OrderReturn' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OrderReturnVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  returnStatus: undefined,
  waybillNo: undefined,
  applyBranchId: undefined,
  applyTime: [],
  returnBranchId: undefined,
  auditTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const loginUserProfile = ref()

// 用于保存当前选中的行
const selectedRows = ref<OrderReturnVO[]>([])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderReturnApi.getOrderReturnPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
// const openForm = (type: string) => {
//   if (selectedRows.value.length === 0) {
//     message.warning(t('error.selectionError'))
//     return
//   } else if (selectedRows.value.length > 1) {
//     message.warning(t('error.selectionOneError'))
//     return
//   }

//   if (type === 'apply') {
//     formRef.value.open(type)
//     return
//   }

//   if (type === 'returnApply') {
//     if (selectedRows.value[0].returnStatus === 4) {
//       formRef.value.open(type, selectedRows.value[0].id)
//       return
//     }
//     message.warning(t('请选择已退货的退货信息!'))
//     return
//   }

//   if (type === 'returnAudit') {
//     if (selectedRows.value[0].returnStatus === 6) {
//       formRef.value.open(type, selectedRows.value[0].id)
//       return
//     }
//     message.warning(t('请选择反退货申请的退货信息!'))
//     return
//   }
//   if (type === 'applyAudit') {
//     if (selectedRows.value[0].returnStatus === 1) {
//       formRef.value.open(type, selectedRows.value[0].id)
//       return
//     }
//     message.warning(t('请选择已申请的退货信息!'))
//     return
//   }
// }
const openForm = (type: string) => {
  if (type === 'apply') {
    formRef.value.open(type)
    return
  }

  if (selectedRows.value.length === 0) {
    message.warning(t('error.selectionError'))
    return
  } else if (selectedRows.value.length > 1) {
    message.warning(t('error.selectionOneError'))
    return
  }

  const row = selectedRows.value[0]

  switch (type) {
    case 'returnApply':
      if (row.returnStatus === 4) {
        formRef.value.open(type, row.id)
      } else {
        message.warning(t('请选择已退货的退货信息!'))
      }
      break

    case 'returnAudit':
      if (row.returnStatus === 6) {
        formRef.value.open(type, row.id)
      } else {
        message.warning(t('请选择反退货申请的退货信息!'))
      }
      break

    case 'applyAudit':
      if (row.returnStatus === 1) {
        formRef.value.open(type, row.id)
      } else {
        message.warning(t('请选择已申请的退货信息!'))
      }
      break
  }
}

/** 异常 */
const updateReturnStatus = async () => {
  if (selectedRows.value.length === 0) {
    message.warning(t('error.selectionError'))
    return
  } else if (selectedRows.value.length > 1) {
    message.warning(t('error.selectionOneError'))
    return
  }
  if (selectedRows.value[0].returnStatus === 1) {
    selectedRows.value[0].returnStatus = 2
    await OrderReturnApi.updateOrderReturn(selectedRows.value[0])
    message.success(t('common.updateSuccess'))
    handleSave(selectedRows.value[0].waybillNo)
  }
  // 刷新列表
  getList()
}

// 保存按钮的点击事件处理函数
const handleSave = async (waybillNo: string) => {
  const eventroute = {
    deptId: loginUserProfile.value.dept.id,
    deptName: loginUserProfile.value.dept.name,
    eventNo: waybillNo,
    // 单号【240925039】取消退货
    eventLog: `单号[${waybillNo}]取消退货`,
    eventNode: '16122',
    eventNodeValue: '取消退货',
    eventType: '16201',
    userCode: loginUserProfile.value.id,
    userName: loginUserProfile.value.username
  }
  await EventRouteApi.createEventRoute(eventroute)
}

/** 处理表格选择变化 */
const handleSelectionChange = (rows: OrderReturnVO[]) => {
  selectedRows.value = rows
  // 安全地访问选中行的信息
  if (rows.length > 0) {
    console.log('选中行ID:', rows[0].id)
  } else {
    console.log('未选中任何行')
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderReturnApi.exportOrderReturn(queryParams)
    download.excel(data, '退货.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted( async() => {
  loginUserProfile.value = await getUserProfile()
  getList()
})
</script>
