<template>

  <!-- 列表 -->
  <ContentWrap>
    <el-table  :data="list" :stripe="true" :show-overflow-tooltip="true"
              style="width: 100%;"
          :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
          :cell-style="{ textAlign: 'center' }">
      <!-- <el-table-column label="id" align="center" prop="id" /> -->
      <el-table-column
        label="改单时间"
        align="center"
        prop="auditTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="改单部门" align="center" prop="auditDeptName" />
      <el-table-column label="改单人" align="center" prop="auditUserName" />
      <el-table-column label="申请部门" align="center" prop="applyDeptName" />
      <el-table-column label="申请人" align="center" prop="applyUserName" />
      <el-table-column
        label="申请时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="改单内容" align="center" prop="editContent" />
      <el-table-column label="改单原因" align="center" prop="applyReason" />
      <!-- <el-table-column label="运单ID" align="center" prop="waybillId" />
      <el-table-column label="运单号" align="center" prop="waybillNo" />
      <el-table-column label="运单状态" align="center" prop="waybillStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.WAYBILL_STATUS" :value="scope.row.waybillStatus" />
        </template>
      </el-table-column>
      <el-table-column label="运单当前部门ID" align="center" prop="currentDeptId" />
      <el-table-column label="运单当前部门名" align="center" prop="currentDeptName" /> -->
      <!-- <el-table-column label="改单状态" align="center" prop="editStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ORDER_EDIT_STATUS" :value="scope.row.editStatus" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="改单类型" align="center" prop="editType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ORDER_EDIT_TYPE" :value="scope.row.editType" />
        </template>
      </el-table-column>
      <el-table-column label="申请人编号" align="center" prop="applyUserId" />
      
      <el-table-column label="申请部门编号" align="center" prop="applyDeptId" />
      
      
      <el-table-column label="申请图片" align="center" prop="applyPictureUrl" />
      <el-table-column label="审批备注" align="center" prop="auditRemark" />
      <el-table-column label="审批人编号" align="center" prop="auditUserId" />
      
      <el-table-column label="审批部门编号" align="center" prop="auditDeptId" /> -->
      


    </el-table>
    <!-- 分页 -->
    <!-- <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    /> -->
  </ContentWrap>

 
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { EditApi, EditVO } from '@/api/wbms/edit'

/** 订单修改 列表 */
defineOptions({ name: 'Edit' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const props = defineProps({
  waybillNo: String
})
const loading = ref(true) // 列表的加载中
const list = ref<EditVO[]>([]) // 列表的数据
// const total = ref(0) // 列表的总页数
// const queryParams = reactive({
//   pageNo: 1,
//   pageSize: 10,
//   waybillNo: undefined,
// })
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  try {
    console.log(props)
    const data = await EditApi.getEditDetailListByWaybillNo(props.waybillNo)
    list.value = data
  } catch (error) {

  }
}

// /** 搜索按钮操作 */
// const handleQuery = () => {
//   queryParams.pageNo = 1
//   getList()
// }

// /** 重置按钮操作 */
// const resetQuery = () => {
//   queryFormRef.value.resetFields()
//   handleQuery()
// }

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await EditApi.deleteEdit(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await EditApi.exportEdit(queryParams)
    download.excel(data, '订单修改.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

defineExpose({
  getList
})

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>